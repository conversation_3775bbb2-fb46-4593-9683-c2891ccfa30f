/**
 * Syrian Identity UI Components
 * 
 * A comprehensive React component library celebrating Syrian culture
 * with RTL-first design, Arabic typography, and cultural authenticity.
 * 
 * @package @sid/components
 * @version 0.1.0
 */

// Button Component
export { Button } from './button';
export type { ButtonProps, ButtonVariant, ButtonSize } from './button';

// Version and metadata
export const version = '0.1.0';
export const name = '@sid/components';

// Component registry for development tools
export const components = {
  Button: 'button'
} as const;
