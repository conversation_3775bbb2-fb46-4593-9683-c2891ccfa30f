/**
 * Syrian Identity UI Components
 * 
 * A comprehensive React component library celebrating Syrian culture
 * with RTL-first design, Arabic typography, and cultural authenticity.
 * 
 * @package @sid/components
 * @version 0.1.0
 */

// Button Component
export { Button } from './button';
export type { ButtonProps, ButtonVariant, ButtonSize } from './button';

// Input Components
export { Input, Textarea, Select } from './input';
export type {
  InputProps, InputVariant, InputSize, InputState,
  TextareaProps, TextareaVariant, TextareaSize, TextareaState,
  SelectProps, SelectVariant, SelectSize, SelectState, SelectOption
} from './input';

// Version and metadata
export const version = '0.1.0';
export const name = '@sid/components';

// Component registry for development tools
export const components = {
  Button: 'button',
  Input: 'input',
  Textarea: 'input',
  Select: 'input'
} as const;
