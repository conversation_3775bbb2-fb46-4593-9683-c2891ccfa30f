{"name": "@acme/ui", "version": "0.0.0", "sideEffects": false, "license": "MIT", "exports": {"./button": {"types": "./src/button.tsx", "import": "./dist/button.mjs", "require": "./dist/button.js"}}, "scripts": {"build": "tsup", "dev": "tsup --watch", "lint": "eslint . --max-warnings 0", "clean": "rm -rf .turbo node_modules dist"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@types/react": "^18.2.61", "@types/react-dom": "^18.2.19", "eslint": "^8.57.0", "@repo/typescript-config": "workspace:*", "tsup": "^8.0.2", "typescript": "5.5.4"}, "dependencies": {"react": "^18.2.0"}, "publishConfig": {"access": "public"}}